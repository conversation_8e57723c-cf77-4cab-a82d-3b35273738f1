import React from "react";
import { Dropdown, Switch } from "antd";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import { useNoiseSuppressionContext } from "../../context/indexContext";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = "",
  microphoneEnabled = true
}) {
  // Get noise suppression state from context
  const { noiseSuppressionEnabled, setNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Noise Suppression Toggle */}
      <div className="noise-suppression-section">
        <div className="noise-suppression-label">
          <span>Noise Suppression</span>
          <small>Reduce background noise</small>
        </div>
        <Switch
          checked={noiseSuppressionEnabled}
          onChange={setNoiseSuppressionEnabled}
          size="small"
          disabled={!microphoneEnabled}
        />
      </div>
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        <span>▼</span>
      </button>
    </Dropdown>
  );
});





export default CombinedAudioDropdown;
