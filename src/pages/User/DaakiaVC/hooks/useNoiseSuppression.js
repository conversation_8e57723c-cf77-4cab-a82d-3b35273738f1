import { useEffect, useRef, useState } from 'react';
import { Track } from 'livekit-client';
import { NoiseSuppressionProcessor } from '@shiguredo/noise-suppression';
import { useNoiseSuppressionContext } from '../context/indexContext';

export const useNoiseSuppression = (room, deviceIdAudio) => {
  const { noiseSuppressionEnabled, setNoiseSuppressionEnabled } = useNoiseSuppressionContext();
  const noiseProcessorRef = useRef(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isNoiseSuppressionActive, setIsNoiseSuppressionActive] = useState(false);

  // Main effect to handle noise suppression toggle
  useEffect(() => {
    if (!room || room.state !== "connected" || isProcessing) return;
    if (noiseSuppressionEnabled === isNoiseSuppressionActive) return;

    const toggleNoiseSuppression = async () => {
      setIsProcessing(true);

      try {
        const audioTrackPublication = room.localParticipant.getTrackPublication(Track.Source.Microphone);
        if (!audioTrackPublication?.track) return;

        const localAudioTrack = audioTrackPublication.track;

        if (noiseSuppressionEnabled) {
          // Enable noise suppression
          noiseProcessorRef.current = new NoiseSuppressionProcessor();
          const processedTrack = await noiseProcessorRef.current.startProcessing(
            localAudioTrack.mediaStreamTrack
          );

          if (processedTrack) {
            await localAudioTrack.replaceTrack(processedTrack, true);
            setIsNoiseSuppressionActive(true);
          }
        } else {
          // Disable noise suppression
          if (noiseProcessorRef.current) {
            await noiseProcessorRef.current.stopProcessing();
            noiseProcessorRef.current = null;
          }

          // Get fresh audio track
          const currentSettings = localAudioTrack.mediaStreamTrack.getSettings();
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              deviceId: currentSettings.deviceId || 'default',
              echoCancellation: true,
              noiseSuppression: false,
              autoGainControl: true
            }
          });

          const newAudioTrack = stream.getAudioTracks()[0];
          if (newAudioTrack) {
            await localAudioTrack.replaceTrack(newAudioTrack, true);
          }

          setIsNoiseSuppressionActive(false);
        }
      } catch (error) {
        console.error('Noise suppression toggle failed:', error);
        setIsNoiseSuppressionActive(false);
      } finally {
        setIsProcessing(false);
      }
    };

    toggleNoiseSuppression();
  }, [room, room?.state, noiseSuppressionEnabled, isProcessing, isNoiseSuppressionActive]);

  // Handle device changes - disable noise suppression
  useEffect(() => {
    if (isNoiseSuppressionActive && deviceIdAudio) {
      setIsNoiseSuppressionActive(false);
      setNoiseSuppressionEnabled(false);
    }
  }, [deviceIdAudio, isNoiseSuppressionActive, setNoiseSuppressionEnabled]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (noiseProcessorRef.current) {
        noiseProcessorRef.current.stopProcessing().catch(console.error);
        noiseProcessorRef.current = null;
      }
    };
  }, []);

  return {
    isNoiseSuppressionActive,
    isProcessing,
  };
};
 